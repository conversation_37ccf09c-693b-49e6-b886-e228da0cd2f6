import { FormArray } from '@angular/forms';
import { CodeName } from '@shared/models/code-name.model';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { Piece } from 'src/app/modules/sli-mgmt/models/piece/piece.model';

export function deepCopy(obj: any): any {
	return JSON.parse(JSON.stringify(obj));
}

export function downloadBinaryFile(res: any, filename: string): void {
	if (!res?.body?.size) return;

	const blob = new Blob([res.body], { type: res.body.type });
	const url = window.URL.createObjectURL(blob);
	const a = document.createElement('a');
	a.href = url;
	a.download = filename;
	a.click();
	window.URL.revokeObjectURL(url);
}

export function roundNumber(num: number, decimalDigits: number): number {
	// @ts-expect-error any
	return +(Math.round(num + `e+${decimalDigits}`) + `e-${decimalDigits}`);
}

export function clearFormArray(formArray: FormArray): void {
	while (formArray.length !== 0) {
		formArray.removeAt(0);
	}
}

export function displayPackagingTypeName(packagingTypes: CodeName[], code: string): string {
	const type = packagingTypes.find((item) => item.code === code);
	const name = type?.name ?? '';
	return name;
}

export function getNvdStatus(piece: Piece | null, type: string): string {
	if (!piece) return type;

	if (piece.nvdForCustoms && type === DropDownType.NCV) {
		return DropDownType.NCV;
	} else if (piece.nvdForCarriage && type === DropDownType.NVD) {
		return DropDownType.NVD;
	} else {
		return DropDownType.YES;
	}
}

import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { SliPieceTableComponent } from '../sli-piece-table/sli-piece-table.component';
import { CommonModule } from '@angular/common';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PieceList } from '../../models/piece/piece-list.model';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { map } from 'rxjs';

@Component({
	selector: 'orll-sli-piece-list',
	templateUrl: './sli-piece-list.component.html',
	styleUrl: './sli-piece-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		CommonModule,
		MatAutocompleteModule,
		SliPieceTableComponent,
		SpinnerComponent,
	],
})
export class SliPieceListComponent extends DestroyRefComponent implements OnInit {
	pieceList: PieceList[] = [];
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};
	totalRecords = 0;
	dataLoading = false;

	@Input() sliNumber = '';
	@Output() saveRequest = new EventEmitter<string>();

	constructor(
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit(): void {
		this.refreshData();
	}

	refreshData() {
		if (this.sliNumber) {
			// for NE-ONE loop timer
			setTimeout(() => {
				this.getPieceListPage(this.pageParams);
			}, 2000);
		}
	}

	// eslint-disable-next-line
	getFormData(ignore?: boolean): {} | null {
		return {
			pieces: [], // update SLI only without piece list data
		};
	}

	onSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
	}

	onPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getPieceListPage(this.pageParams);
	}

	private getPieceListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;
		this.pieceList = [];

		this.sliCreateRequestService
			.getPieceList(pageParams, this.sliNumber)
			.pipe(
				map((response) => {
					const treeNodes = response.rows.map((item) => ({
						...item,
						level: 0,
						expanded: false,
					}));
					return {
						rows: treeNodes,
						total: response.total,
					};
				}),
				takeUntilDestroyed(this.destroyRef)
			)
			.subscribe({
				next: (res) => {
					this.pieceList = res.rows;
					this.totalRecords = res.total;
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}
}

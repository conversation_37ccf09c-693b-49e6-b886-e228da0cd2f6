<div class="orll-sli-piece-table__container">
	@if (!this.hawbId) {
		<div class="orll-sli-piece-table__create">
			<div class="orll-sli-piece-table__table_info">
				<div class="label">{{ 'sli.mgmt.list.total.quantity' | translate }}:</div>
				<div>{{ totalPiece }}</div>
				<div class="label">{{ 'sli.mgmt.list.total.slac' | translate }}:</div>
				<div>{{ totalSlac }}</div>
			</div>
			<div class="orll-sli-piece-table__table__btn">
				<button
					mat-flat-button
					color="primary"
					(click)="consolidatePiece()"
					[disabled]="dataSource.data.length === 0 || !selection.hasValue() || selection.selected.length < 2">
					<mat-icon>view_in_ar</mat-icon>
					{{ 'sli.piece.consolidate.title' | translate }}
				</button>
				<button
					mat-stroked-button
					color="primary"
					[disabled]="dataSource.data.length === 0 || !selection.hasValue()"
					(click)="delPiece($event, selection?.selected)"
					class="orll-sli-piece-table__delete-button">
					<mat-icon>delete</mat-icon>
					{{ 'sli.mgmt.delete.piece' | translate }}
				</button>
				<button mat-flat-button color="primary" (click)="addPiece()" class="orll-sli-piece-table__add-button">
					<mat-icon>add</mat-icon>
					{{ 'sli.mgmt.add.piece' | translate }}
				</button>
			</div>
		</div>
	} @else {
		<div class="orll-sli-piece-table__hawb-tab">
			<span>{{ 'hawb.table.column.hawbNumber' | translate }}: </span>
			<span class="bold">{{ hawbNumber }}</span>
			<span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
			<span>{{ 'hawb.mgmt.associated.sli' | translate }}: </span>
			<span class="bold">{{ sliNumber }}</span>
			<span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
			<span>{{ 'hawb.mgmt.total.piece' | translate }}: </span>
			<span class="bold">{{ totalPiece }}</span>
			<span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
			<span>{{ 'hawb.mgmt.fhl.total.slac' | translate }}: </span>
			<span class="bold">{{ totalSlac }}</span>
			<span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
			<span>{{ 'hawb.mgmt.latest.status' | translate }}: </span>
			<span class="bold">{{ latestStatus }}</span>
		</div>
	}
	<div class="orll-sli-piece-table__table-container">
		<table
			mat-table
			[dataSource]="dataSource"
			[trackBy]="trackByPieceId"
			matSort
			(onScroll)="onScrollChange.emit($event)"
			(matSortChange)="onSortChange($event)"
			aria-label="SLI Piece table"
			class="orll-sli-piece-table__table">
			<ng-container matColumnDef="select">
				<th mat-header-cell *matHeaderCellDef class="orll-sli-piece-table__tree_col_head">
					<mat-checkbox
						(change)="toggleAllRows()"
						[checked]="isAllSelected()"
						[disabled]="dataSource.data.length === 0"
						[indeterminate]="selection.hasValue() && !isAllSelected()">
					</mat-checkbox>
				</th>
				<td mat-cell *matCellDef="let row" class="orll-sli-piece-table__tree_col">
					@if (row.level === 0) {
						<mat-checkbox
							(click)="$event.stopPropagation()"
							(keydown.enter)="$event.stopPropagation()"
							(change)="selection.toggle(row)"
							[checked]="selection.isSelected(row)">
						</mat-checkbox>
					}

					@if (row.containedPieces?.length > 0) {
						<button mat-icon-button (click)="toggleNode(row)" class="orll-sli-piece-table__tree_col_btn">
							<mat-icon fontSet="material-icons-outlined" class="orll-sli-piece-table__tree_col_icon">
								{{ row.expanded ? 'indeterminate_check_box_outlined' : 'add_box_outlined' }}
							</mat-icon>
						</button>
					}
				</td>
			</ng-container>

			<ng-container matColumnDef="productDescription" class="">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="orll-sli-piece-table__description">
					{{ 'sli.piece.table.column.productDescription' | translate }}
				</th>
				<td mat-cell *matCellDef="let record" class="orll-sli-piece-table__description">
					{{ record.productDescription }}
				</td>
			</ng-container>

			<ng-container matColumnDef="packagingType">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
					{{ 'sli.piece.table.column.packagingType' | translate }}
				</th>
				<td mat-cell *matCellDef="let record">{{ record.packagingType }}</td>
			</ng-container>

			<ng-container matColumnDef="grossWeight">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
					{{ 'sli.piece.table.column.grossWeight' | translate }}
				</th>
				<td mat-cell *matCellDef="let record">{{ record.grossWeight }}</td>
			</ng-container>

			<ng-container matColumnDef="dimensions">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'sli.piece.table.column.dimensions' | translate }}</th>
				<td mat-cell *matCellDef="let record">
					{{ record.dimensions?.length }}CMx{{ record.dimensions?.width }}CMx{{ record.dimensions?.height }}CM
				</td>
			</ng-container>

			<ng-container matColumnDef="pieceQuantity">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
					{{ 'sli.piece.table.column.pieceQuantity' | translate }}
				</th>
				<td mat-cell *matCellDef="let record">{{ record.pieceQuantity }}</td>
			</ng-container>

			<ng-container matColumnDef="slac">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'sli.piece.table.column.slac' | translate }}</th>
				<td mat-cell *matCellDef="let record">{{ record.slac }}</td>
			</ng-container>

			<ng-container matColumnDef="latestStatus">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
					{{ 'sli.piece.table.column.latestStatus' | translate }}
				</th>
				<td mat-cell *matCellDef="let record">
					<a class="latest-status__link" (click)="openHistory(record.pieceId, 'hawb')">
						{{ record.latestStatus }}
					</a>
				</td>
			</ng-container>

			<ng-container matColumnDef="actions">
				<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'sli.piece.table.column.actions' | translate }}</th>
				<td mat-cell *matCellDef="let record">
					@if (record.level === 0 && !record.containedPieces) {
						<button
							mat-icon-button
							color="primary"
							(click)="editPiece($event, record)"
							class="orll-sli-piece-table__edit-button">
							<mat-icon>edit</mat-icon>
						</button>
					}
					@if (record.containedPieces?.length > 0) {
						<button
							mat-icon-button
							color="primary"
							(click)="editConsolidatePiece($event, record)"
							class="orll-sli-piece-table__edit-button">
							<mat-icon>edit</mat-icon>
						</button>

						<button
							mat-icon-button
							color="primary"
							(click)="unConsolidatePiece(record)"
							class="orll-sli-piece-table__row-delete-button">
							<mat-icon>design_services_rounded</mat-icon>
						</button>
					} @else if (record.level !== 1) {
						<button
							mat-icon-button
							color="primary"
							(click)="delPiece($event, [record])"
							class="orll-sli-piece-table__row-delete-button">
							<mat-icon>delete</mat-icon>
						</button>
					}
				</td>
			</ng-container>

			<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
			<tr mat-row *matRowDef="let record; columns: displayedColumns" class="orll-sli-piece-table__row"></tr>
		</table>
	</div>

	@if (!this.hawbId) {
		<mat-paginator [pageSizeOptions]="tablePageSizes" [length]="totalRecords" (page)="pagination.emit($event)"></mat-paginator>
	}

	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>

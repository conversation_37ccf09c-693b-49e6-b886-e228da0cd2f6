<div class="orll-hawb-table__container">
	@if ((hasPermission(createPermission, hawbModule) | async) && !fromCreateMawb) {
		<div class="orll-hawb-table__create">
			<button mat-flat-button color="primary" (click)="createHawbFromSli()">
				<mat-icon>add</mat-icon>
				{{ 'hawb.mgmt.create' | translate }}
			</button>
		</div>
	}
	@if ((hasPermission(createPermission, mawbModule) | async) && fromCreateMawb && !isFHL) {
		<div class="orll-hawb-table__create">
			<button
				mat-flat-button
				color="primary"
				[disabled]="dataSource.data.length === 0 || !selection.hasValue()"
				(click)="createMawbFromHawb()">
				<mat-icon>add</mat-icon>
				{{ 'mawb.mgmt.create.fromSelectedHawb' | translate }}
			</button>
		</div>
	}
	@if (this.isFHL) {
		<div class="orll-hawb-table__fhl-header">
			<span>{{ 'hawb.mgmt.fhl.total' | translate }}: </span>
			<span class="bold">{{ totalRecords }}</span>
			<span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
			<span>{{ 'hawb.mgmt.fhl.total.slac' | translate }}: </span>
			<span class="bold">{{ totalSlac }}</span>
			<span>&nbsp;&nbsp;|&nbsp;&nbsp;</span>
			<span>{{ 'hawb.mgmt.fhl.total.piece' | translate }}: </span>
			<span class="bold">{{ totalPiece }}</span>
		</div>
		<mat-divider></mat-divider>
	}
	<table
		mat-table
		[dataSource]="dataSource"
		[trackBy]="trackByHawbId"
		matSort
		(matSortChange)="onSortChange($event)"
		aria-label="HAWB table"
		class="orll-hawb-table__mat">
		<ng-container matColumnDef="select">
			<th mat-header-cell *matHeaderCellDef class="hawb-select-width">
				<mat-checkbox
					(change)="toggleAllRows()"
					[checked]="isAllSelected()"
					[disabled]="dataSource.data.length === 0"
					[indeterminate]="selection.hasValue() && !isAllSelected()">
				</mat-checkbox>
			</th>
			<td mat-cell *matCellDef="let row">
				<mat-checkbox
					(click)="$event.stopPropagation()"
					(keydown.enter)="$event.stopPropagation()"
					(change)="selection.toggle(row)"
					[checked]="selection.isSelected(row)">
				</mat-checkbox>
			</td>
		</ng-container>

		<ng-container matColumnDef="hawbNumber">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="hawb-number-width">
				{{ 'hawb.table.column.hawbNumber' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">
				<a class="hawb-number__link" (click)="editHawb(record.hawbId)">
					{{ record.hawbNumber }}
				</a>
			</td>
		</ng-container>

		<ng-container matColumnDef="shipper">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="hawb-shipper-width">
				{{ 'hawb.table.column.shipper' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.shipper }}</td>
		</ng-container>

		<ng-container matColumnDef="consignee">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="hawb-consignee-width">
				{{ 'hawb.table.column.consignee' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.consignee }}</td>
		</ng-container>

		<ng-container matColumnDef="goodsDescription">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="hawb-description-width">
				{{ 'hawb.table.column.goodsDescription' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.goodsDescription }}</td>
		</ng-container>

		<ng-container matColumnDef="origin">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="hawb-airport-width">
				{{ 'hawb.table.column.origin' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.origin }}</td>
		</ng-container>

		<ng-container matColumnDef="destination">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="hawb-airport-width">
				{{ 'hawb.table.column.destination' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.destination }}</td>
		</ng-container>

		<ng-container matColumnDef="createDate">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="hawb-date-width">
				{{ 'hawb.table.column.createDate' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.createDate }}</td>
		</ng-container>

		<ng-container matColumnDef="latestStatus">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="latest-status-width">
				{{ 'hawb.table.column.latestStatus' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.latestStatus }}</td>
		</ng-container>

		<ng-container matColumnDef="eventDate">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="event-date-width">
				{{ 'hawb.table.column.eventDate' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.eventDate }}</td>
		</ng-container>

		<ng-container matColumnDef="mawbNumber">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="mawb-number-width">
				{{ 'hawb.table.column.mawbNumber' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.mawbNumber }}</td>
		</ng-container>

		<ng-container matColumnDef="pieceQuantity">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="quantity-width">
				{{ 'hawb.table.column.pieceNumber' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.pieceQuantity }}</td>
		</ng-container>

		<ng-container matColumnDef="totalGrossWeight">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="weight-width">
				{{ 'hawb.table.column.totalGrossWeight' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.totalGrossWeight }}</td>
		</ng-container>

		<ng-container matColumnDef="slac">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="slac-width">
				{{ 'hawb.table.column.slac' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.slac }}</td>
		</ng-container>

		<ng-container matColumnDef="countryCode">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="country-code-width">
				{{ 'hawb.table.column.countryCode' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.countryCode }}</td>
		</ng-container>

		<ng-container matColumnDef="contentCode">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="content-code-width">
				{{ 'hawb.table.column.contentCode' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.contentCode }}</td>
		</ng-container>

		<ng-container matColumnDef="otherCustomsInformation">
			<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header class="other-info-width">
				{{ 'hawb.table.column.otherCustomsInformation' | translate }}
			</th>
			<td mat-cell *matCellDef="let record">{{ record.otherCustomsInformation }}</td>
		</ng-container>

		<ng-container matColumnDef="share">
			<th scope="col" mat-header-cell *matHeaderCellDef class="hawb-share-width">{{ 'hawb.table.column.share' | translate }}</th>
			<td mat-cell *matCellDef="let record">
				<button mat-icon-button aria-label="Share a HAWB record" class="share-button" (click)="shareHawb.emit(record)">
					<mat-icon>share</mat-icon>
				</button>
			</td>
		</ng-container>

		<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
		<tr mat-row *matRowDef="let record; columns: displayedColumns" class="orll-hawb-table__row"></tr>
	</table>
</div>

<mat-paginator [pageSizeOptions]="tablePageSizes" [length]="totalRecords" (page)="pagination.emit($event)"></mat-paginator>

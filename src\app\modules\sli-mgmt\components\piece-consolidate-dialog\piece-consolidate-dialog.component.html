<div class="orll-piece-consolidate-dialog">
	<h2 mat-dialog-title>{{ 'sli.piece.consolidate.title' | translate }}</h2>
	<mat-dialog-content style="margin-top: 30px">
		<form [formGroup]="pieceInfoForm">
			<div class="row">
				<mat-form-field appearance="outline" class="col-12" floatLabel="always">
					<mat-label>{{ 'sli.piece.table.column.productDescription' | translate }}</mat-label>
					<textarea rows="4" matInput formControlName="productDescription"></textarea>
				</mat-form-field>
			</div>
			<div class="row">
				<div class="col-3">
					<mat-form-field appearance="outline" floatLabel="always" class="input-box">
						<mat-label>{{ 'sli.piece.table.column.grossWeight' | translate }}</mat-label>
						<div matInput class="unit-row">
							<input matInput formControlName="grossWeight" required />
							<div matSuffix class="weight-unit">KG</div>
						</div>
					</mat-form-field>
				</div>

				<div class="col-9">
					<mat-form-field appearance="outline" floatLabel="always" class="input-box">
						<mat-label> {{ 'sli.piece.table.column.dimensions' | translate }}</mat-label>
						<div matInput class="all-inputs-row">
							<input matInput formControlName="dimLength" required (click)="$event.stopPropagation()" />
							<span matSuffix class="input_suffix">CM</span>
							<span class="divider"></span>
							<input matInput formControlName="dimWidth" required (click)="$event.stopPropagation()" />
							<span matSuffix class="input_suffix">CM</span>
							<span class="divider"></span>
							<input matInput formControlName="dimHeight" required (click)="$event.stopPropagation()" />
							<span matSuffix class="input_suffix">CM </span>
						</div>
					</mat-form-field>
				</div>
			</div>

			<div class="row">
				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label> {{ 'sli.piece.table.column.packagingType' | translate }}</mat-label>
					<mat-select formControlName="packagingType">
						@for (opt of packagingTypes; track opt.code) {
							<mat-option [value]="opt.code">{{ opt.name }}</mat-option>
						}
					</mat-select>
				</mat-form-field>
				<mat-form-field appearance="outline" floatLabel="always" class="input-box col-3">
					<mat-label>{{ 'sli.dgPiece.formItem.upid' | translate }}</mat-label>
					<div matInput class="unit-row">
						<input matInput formControlName="upid" />
					</div>
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label>{{ 'sli.liveAnimalPiece.formItem.packagedIdentifier' | translate }}</mat-label>
					<input matInput formControlName="packagedIdentifier" />
				</mat-form-field>

				<mat-form-field appearance="outline" floatLabel="always" class="input-box col-3">
					<mat-label>{{ 'sli.piece.hsCommodityDescription' | translate }}</mat-label>
					<div matInput class="unit-row">
						<input matInput formControlName="hsCommodityDescription" />
					</div>
				</mat-form-field>
			</div>

			<div class="row">
				<mat-form-field appearance="outline" class="col-4" floatLabel="always">
					<mat-label>{{ 'sli.dgPiece.formItem.whetherHaveDeclaredValueForCustoms' | translate }}</mat-label>
					<mat-select formControlName="nvdForCustoms">
						<mat-option [value]="true">NCV</mat-option>
						<mat-option [value]="false">Yes</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" class="col-4" floatLabel="always">
					<mat-label>{{ 'sli.dgPiece.formItem.whetherHaveDeclaredValueForCarriage' | translate }}</mat-label>
					<mat-select formControlName="nvdForCarriage">
						<mat-option [value]="true">NVD</mat-option>
						<mat-option [value]="false">Yes</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" floatLabel="always" class="input-box col-4">
					<mat-label>{{ 'sli.dgPiece.formItem.shippingMarks' | translate }}</mat-label>
					<div matInput class="unit-row">
						<input matInput formControlName="shippingMarks" />
					</div>
				</mat-form-field>
			</div>

			<div class="row">
				<mat-form-field appearance="outline" class="col-12" floatLabel="always">
					<mat-label>{{ 'sli.dgPiece.formItem.textualHandlingInstructions' | translate }}</mat-label>
					<textarea rows="4" matInput formControlName="textualHandlingInstructions"></textarea>
				</mat-form-field>
			</div>

			<div class="row">
				<table mat-table [dataSource]="dataSource" matSort aria-label="Company table">
					<ng-container matColumnDef="pieceDescription">
						<th scope="col" mat-header-cell *matHeaderCellDef>{{ 'sli.piece.table.column.productDescription' | translate }}</th>
						<td mat-cell *matCellDef="let row">
							{{ row.productDescription }}
						</td>
					</ng-container>

					<ng-container matColumnDef="packagingType">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
							{{ 'sli.piece.table.column.packagingType' | translate }}
						</th>
						<td mat-cell *matCellDef="let row">{{ row.packagingType }}</td>
					</ng-container>

					<ng-container matColumnDef="grossWeight">
						<th scope="col" mat-header-cell *matHeaderCellDef>{{ 'sli.piece.table.column.grossWeight' | translate }}</th>
						<td mat-cell *matCellDef="let row">
							{{ row.grossWeight }}
						</td>
					</ng-container>

					<ng-container matColumnDef="dimensions">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
							{{ 'sli.piece.table.column.dimensions' | translate }}
						</th>
						<td mat-cell *matCellDef="let row">
							{{
								`${row.dimensions?.length}${row.dimensions?.unit ?? 'CM'} * ${row.dimensions?.width}${row.dimensions?.unit ?? 'CM'}
								* ${row.dimensions?.height}${row.dimensions?.unit ?? 'CM'} `
							}}
						</td>
					</ng-container>

					<ng-container matColumnDef="quantity">
						<th scope="col" mat-header-cell *matHeaderCellDef mat-sort-header>
							{{ 'sli.piece.table.column.pieceQuantity' | translate }}
						</th>
						<td mat-cell *matCellDef="let row">{{ row.pieceQuantity }}</td>
					</ng-container>

					<ng-container matColumnDef="delete">
						<th scope="col" mat-header-cell *matHeaderCellDef></th>
						<td mat-cell *matCellDef="let row">
							<button mat-icon-button color="primary" aria-label="Delete record" (click)="deletePiece($event, row)">
								<mat-icon>delete</mat-icon>
							</button>
						</td>
					</ng-container>
					<tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
					<tr mat-row *matRowDef="let row; columns: displayedColumns" class="orll-mawb-table__row"></tr>
				</table>
			</div>
			<div class="orll-piece-consolidate-dialog__btn">
				<button mat-stroked-button color="primary" [matDialogClose]="'cancel'">{{ 'common.dialog.cancel' | translate }}</button>
				<button mat-flat-button color="primary" (click)="consolidatePiece()" [disabled]="dataSource.data.length === 0">
					<mat-icon>view_in_ar</mat-icon>
					{{ 'sli.piece.consolidate.btn' | translate }}
				</button>
			</div>
		</form>
	</mat-dialog-content>
	@if (dataLoading) {
		<iata-spinner></iata-spinner>
	}
</div>

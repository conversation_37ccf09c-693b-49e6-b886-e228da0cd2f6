import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { CodeName } from '@shared/models/code-name.model';
import { AIRPORTS } from '../ref-data/airports.data';
import { Airport } from '../models/airport.model';
import { COUNTRIES } from '../ref-data/countries.data';
import { CURRENCIES } from '../ref-data/currencies.data';
import { INCOTERMS } from '../ref-data/incoterms.data';
import { Country } from '../models/country.model';
import { Province } from '../models/province.model';
import { SliCreatePayload } from '../models/sli-create-payload.model';
import { ConsolidatePiece, Piece } from '../models/piece/piece.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { PieceList } from '../models/piece/piece-list.model';

@Injectable({ providedIn: 'root' })
export class SliCreateRequestService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	createSli(sliCreatePayload: SliCreatePayload): Observable<string> {
		return super.postData<string>('sli', sliCreatePayload);
	}

	updateSli(sliCreatePayload: SliCreatePayload, id: string): Observable<string> {
		return super.updateDataPatch<string>('sli', { id, ...sliCreatePayload });
	}

	getSliDetail(sliId: string): Observable<SliCreatePayload> {
		return super.getData<SliCreatePayload>('sli/detail', { sliId });
	}

	createPiece(piecePayload: Piece): Observable<string> {
		return super.postData<string>('sli/piece', piecePayload);
	}

	updatePiece(piecePayload: Piece, id: string): Observable<string> {
		return super.updateDataPatch<string>('sli/piece', { id, ...piecePayload });
	}

	createDgPiece(piecePayload: Piece): Observable<string> {
		return super.postData<string>('sli/dgPiece', piecePayload);
	}

	updateDgPiece(piecePayload: Piece, id: string): Observable<string> {
		return super.updateDataPatch<string>('sli/dgPiece', { id, ...piecePayload });
	}

	createLaPiece(piecePayload: Piece): Observable<string> {
		return super.postData<string>('sli/laPiece', piecePayload);
	}

	updateLaPiece(piecePayload: Piece, id: string): Observable<string> {
		return super.updateDataPatch<string>('sli/laPiece', { id, ...piecePayload });
	}

	deletePiece(ids: string[], sliId: string): Observable<string> {
		return super.deleteData<string>('sli/piece', { pieceIdList: ids, sliId });
	}

	getPieceDetail(pieceId: string): Observable<Piece> {
		return super.getData<Piece>('sli/piece/detail', { pieceId });
	}

	getPieceList(pageParams: PaginationRequest, sliId: string): Observable<PaginationResponse<PieceList>> {
		return super.getData<PaginationResponse<PieceList>>('sli/piece/list', {
			...pageParams,
			sliId,
		});
	}

	consolidatePiece(param: ConsolidatePiece): Observable<boolean> {
		return super.postData<boolean>('sli/piece/consolidate', param);
	}

	unConsolidatePiece(param: { pieceId: string; containedPieceIds: string[]; sliId: string }): Observable<boolean> {
		return super.postData<boolean>('sli/piece/unConsolidate', param);
	}

	getCountries(code?: string): Observable<Country[]> {
		return of(COUNTRIES).pipe(
			map((countries: Country[]) => {
				return countries
					.filter((country: Country) => !code || country.code === code)
					.map((country: Country) => {
						return {
							code: country.code,
							name: country.name,
							provinces: country.provinces,
						};
					});
			})
		);
	}

	getProvinces(country: Country, code?: string): Observable<Province[]> {
		return of(country.provinces).pipe(
			map((provinces: Province[]) => {
				return provinces
					.filter((province: Province) => !code || province.code === code)
					.map((province: Province) => {
						return {
							code: province.code,
							name: province.name,
							cities: province.cities,
						};
					});
			})
		);
	}

	getCities(province: Province): Observable<CodeName[]> {
		return of(province.cities).pipe(
			map((cities: CodeName[]) => {
				return cities.map((city: CodeName) => {
					return { code: city.code, name: city.name };
				});
			})
		);
	}

	getAirports(): Observable<CodeName[]> {
		return of(AIRPORTS).pipe(
			map((airports: Airport[]) => {
				return airports.map((airport: Airport) => {
					return { code: airport.code, name: `${airport.code} - ${airport.name}` };
				});
			})
		);
	}

	getCurrencies(): Observable<string[]> {
		return of(CURRENCIES).pipe(
			map((currencies: string[]) => {
				return currencies.map((currency: string) => {
					return currency;
				});
			})
		);
	}

	getIncoterms(): Observable<CodeName[]> {
		return of(INCOTERMS).pipe(
			map((incoterms: CodeName[]) => {
				return incoterms.map((incoterm: CodeName) => {
					return { code: incoterm.code, name: `${incoterm.code} - ${incoterm.name}` };
				});
			})
		);
	}

	getPackingTypes(): Observable<CodeName[]> {
		return super.getData<CodeName[]>('sys-management/enums/packageType').pipe(
			map((res) => {
				return res.map((packageType: CodeName) => {
					return { code: packageType.code, name: packageType.name };
				});
			})
		);
	}
}

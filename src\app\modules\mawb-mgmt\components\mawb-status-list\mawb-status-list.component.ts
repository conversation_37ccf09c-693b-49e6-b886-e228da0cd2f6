import { ChangeDetectorRef, Component, HostListener, Input, OnInit } from '@angular/core';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { HAWBEventListObj, HAWBEventObj, MAWBEventListObj, MAWBEventObj, PieceEventListObj } from '../../models/mawb-event.model';
import { RolesAwareComponent } from '@shared/components/roles-aware/roles-aware.component';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MawbStatusUpdateComponent } from '../../components/mawb-status-update/mawb-status-update.component';
import { MawbStatusService } from '../../services/mawb-status.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { map } from 'rxjs';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { StatusHistoryComponent } from '../status-history/status-history.component';

const pageParams = {
	pageNum: 1,
	pageSize: 20,
	orderByColumn: '',
	isAsc: 'asc',
};

@Component({
	selector: 'orll-mawb-status',
	imports: [
		ScrollingModule,
		MatExpansionModule,
		MatCheckboxModule,
		MatTableModule,
		MatIconModule,
		TranslateModule,
		MatButtonModule,
		MatDialogModule,
		SpinnerComponent,
	],
	templateUrl: './mawb-status-list.component.html',
	styleUrl: './mawb-status-list.component.scss',
})
export class MawbStatusListComponent extends RolesAwareComponent implements OnInit {
	@Input() mawbId = '';
	@Input() tabLabel = '';

	currentPageNum = 1;
	anyItemChecked = false;
	currentUserOrgType = '';
	orgTypes = ['GHA', 'Airport Security', 'Custom'];

	mawbStatus: MAWBEventListObj = {
		mawbId: '',
		latestStatus: '',
		updateTime: '',
		orgName: '',
		userName: '',
		updateBy: '',
		checked: false,
		opened: false,
		code: '',
		eventDate: '',
	};
	hawbStatusList: HAWBEventListObj[] = [];
	piceStatusList: PieceEventListObj[] = [];

	noMoreHawb = false;

	constructor(
		private readonly cdr: ChangeDetectorRef,
		private readonly dialog: MatDialog,
		private readonly statusService: MawbStatusService,
		private readonly tranlateService: TranslateService
	) {
		super();
	}

	dataSource = new MatTableDataSource<PieceEventListObj>(this.piceStatusList);
	dataLoading = false;

	ngOnInit(): void {
		this.getCurrentUser()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((user) => {
				this.currentUserOrgType = user?.orgType || '';
			});
		//it will be passed into the component
		this.initData();
	}

	private initData() {
		if (this.mawbId) {
			this.dataLoading = true;
			this.statusService
				.getMawbStatus({ loId: this.mawbId, type: 'mawb' })
				.pipe(takeUntilDestroyed(this.destroyRef))
				.subscribe((res) => {
					this.dataLoading = false;
					this.mawbStatus = { ...res, mawbId: this.mawbId, checked: false };
					this.cdr.markForCheck();
				});
			this.listHawbByMawbId();
		}
	}

	private listHawbByMawbId() {
		this.statusService
			.listHawbStatusByMawb({ ...pageParams, pageNum: this.currentPageNum, mawbId: this.mawbId })
			.pipe(
				map((response) => ({
					...response,
					rows: response.rows.map((row) => ({
						...row,
						pageNum: 1,
						checked: false,
						opened: false,
					})),
				})),
				takeUntilDestroyed(this.destroyRef)
			)
			.subscribe((data) => {
				this.dataLoading = false;
				this.hawbStatusList = [...this.hawbStatusList, ...data.rows];
				this.currentPageNum += 1;
				if (this.hawbStatusList.length === data.total) {
					this.noMoreHawb = true;
				}
				this.cdr.markForCheck();
			});
	}

	@HostListener('window:scroll', [])
	onWindowScroll(): void {
		const scrollTop = window.scrollY || document.documentElement.scrollTop || 0;
		const scrollHeight = document.documentElement.scrollHeight;
		const clientHeight = window.innerHeight;
		if (!this.mawbId || this.tabLabel !== this.tranlateService.instant('mawb.event.tracking.title')) {
			return;
		}

		if (scrollHeight - (scrollTop + clientHeight) < 100 && !this.dataLoading && !this.noMoreHawb) {
			if (this.mawbId && this.tabLabel === this.tranlateService.instant('mawb.event.tracking.title')) {
				this.listHawbByMawbId();
			}
		}
	}

	onScroll(event: Event, hawb: HAWBEventListObj) {
		const div = event.target as HTMLDivElement;
		const atBottom = (div.scrollHeight - div.scrollTop) === div.clientHeight;

		if (atBottom && !this.dataLoading && !hawb.noMorePieces) {
			this.loadPieces(hawb);
		}
	}

	toggleMawb() {
		if (!this.mawbStatus) {
			return;
		}
		this.mawbStatus.checked = !this.mawbStatus.checked;
		//select / deselect all hawb and pieces
		this.hawbStatusList.forEach((item) => {
			item.checked = this.mawbStatus.checked;
			if (item.pieceStatusList) {
				item.pieceStatusList.forEach((piece) => (piece.checked = this.mawbStatus.checked));
			}
		});
		this.cdr.markForCheck();
	}

	toggleHawb(row: HAWBEventListObj) {
		row.checked = !row.checked;
		row.pieceStatusList.forEach((item) => (item.checked = row.checked));
		if (this.mawbStatus) {
			this.mawbStatus.checked = this.hawbStatusList.every((item) => item.checked);
		}
		this.cdr.markForCheck();
	}

	togglePiece(hawb: HAWBEventListObj, piece: PieceEventListObj) {
		piece.checked = !piece.checked;
		let piecesChecked = piece.checked;
		//if uncheck the pieces, then check whether its hawb should be uncheck
		if (!piecesChecked) {
			piecesChecked = hawb.pieceStatusList.some((item) => item.checked);
		}
		// pieces of this hawb are selected and mawb is unchecked, then clear other hawb and its pieces.
		if (piecesChecked && !this.mawbStatus.checked) {
			this.uncheckOtherHawb(hawb);
		}

		hawb.checked = hawb.pieceStatusList.every((item) => item.checked);
		this.mawbStatus.checked = this.hawbStatusList.every((item) => item.checked);
	}

	private uncheckOtherHawb(hawb: HAWBEventListObj) {
		this.hawbStatusList
			.filter((item) => item.hawbId !== hawb.hawbId)
			.forEach((item) => {
				item.checked = false;
				if (item.pieceStatusList) {
					item.pieceStatusList.forEach((item) => (item.checked = false));
				}
			});
	}

	bulkUpdate() {
		if (!this.mawbStatus) {
			//should not be here
			return;
		}
		let param: MAWBEventObj = {
			mawbId: this.mawbStatus.mawbId,
			choseAllHAWB: this.mawbStatus.checked,
			hawbIdList: [],
		};
		let selectedHawbList: HAWBEventObj[];
		if (!this.mawbStatus.checked) {
			selectedHawbList = this.hawbStatusList
				.filter((hawb) => hawb.checked || hawb.pieceStatusList?.some((piece) => piece.checked))
				.map((item) => {
					let selectedPieceIdList: string[] = [];
					let choseAllPieces = false;
					if (item.pieceStatusList) {
						selectedPieceIdList = item.pieceStatusList.filter((piece) => piece.checked).map((item) => item.pieceId);
						choseAllPieces = item.pieceStatusList.length > 0 && item.pieceStatusList.length === selectedPieceIdList.length;
					}
					return {
						hawbId: item.hawbId,
						choseAllPiece: choseAllPieces,
						pieceIdList: choseAllPieces ? [] : selectedPieceIdList,
					};
				});
			param = { ...param, hawbIdList: selectedHawbList };
		}

		const dialogRef = this.dialog.open(MawbStatusUpdateComponent, {
			width: '50vw',
			autoFocus: false,
			data: {
				param: param,
			},
		});
		dialogRef.afterClosed().subscribe(() => {
			this.initData();
		});
	}

	togglePanel(hawb: HAWBEventListObj) {
		hawb.opened = !hawb.opened;
		if (hawb.opened && (!hawb.pieceStatusList || hawb.pieceStatusList.length === 0)) {
			hawb.pieceStatusList = [];
			this.loadPieces(hawb);
		}
		if (this.mawbStatus.checked) {
			return;
		}
		if (hawb.checked) {
			this.uncheckOtherHawb(hawb);
			this.cdr.markForCheck();
		}
	}

	loadPieces(hawb: HAWBEventListObj) {
		this.dataLoading = true;
		this.statusService
			.listPieceStatusByHawb({ ...pageParams, pageNum: hawb.pageNum ? hawb.pageNum : 1, hawbId: hawb.hawbId })
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					res.rows.forEach((item) => (item.checked = hawb.checked));
					hawb.pieceStatusList = [...hawb.pieceStatusList, ...res.rows];
					this.dataLoading = false;
					hawb.pageNum += 1;
					if (hawb.pieceStatusList.length === res.total) {
						hawb.noMorePieces = true;
					}
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}

	updateBtnDisabled() {
		if (!this.mawbStatus) {
			return true;
		}
		if (this.mawbStatus.checked) {
			return false;
		}
		if (this.hawbStatusList) {
			//some hawb checked or some pieces is checked
			const anyHawbOrPiecesChecked = this.hawbStatusList.some(
				(item) => item.checked || (item.pieceStatusList ?? []).some((piece) => piece.checked)
			);
			if (anyHawbOrPiecesChecked) {
				return false;
			}
		}
		return true;
	}

	openHistory(loId: string, type: string) {
		this.dialog.open(StatusHistoryComponent, {
			width: '60vw',
			autoFocus: false,
			data: {
				loId,
				type,
			},
		});
	}
}

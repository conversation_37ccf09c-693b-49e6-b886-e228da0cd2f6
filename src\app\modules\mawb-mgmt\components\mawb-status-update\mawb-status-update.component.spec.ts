import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MawbStatusUpdateComponent } from './mawb-status-update.component';
import { MawbStatusService } from '../../services/mawb-status.service';
import { HTTP_INTERCEPTORS, provideHttpClient } from '@angular/common/http';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { of } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { EventTimeType } from '../../models/mawb-event.model';

describe('MawbStatusUpdateComponent', () => {
	let component: MawbStatusUpdateComponent;
	let fixture: ComponentFixture<MawbStatusUpdateComponent>;
	const mockDialogData = {
		param: {
			mawbId: 'test1',
			choseAllHAWB: true,
			hawbIdList: [
				{
					hawbId: 'hawb1',
					choseAllPiece: false,
					pieceIdList: [],
				},
			],
		},
	};

	const mockStatusService = {
		getEventList: jasmine.createSpy('getEventList').and.returnValue(of(['3333', '3331'])),
		getMawbStatus: jasmine.createSpy('getMawbStatus').and.returnValue(of({ mawbId: '11' })),
		listHawbStatusByMawb: jasmine.createSpy('listHawbStatusByMawb').and.returnValue(of({ rows: [{ hawbId: '1' }] })),
		listPieceStatusByHawb: jasmine.createSpy('listPieceStatusByHawb').and.returnValue(of({ rows: [{ id: '1' }] })),
		updateEventStatus: jasmine.createSpy('updateEventStatus').and.returnValue(of('Success')),
		getAllEvents: jasmine.createSpy('updateEventStatus').and.returnValue(of([{ code: '3333', name: '3331' }])),
	};

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [MawbStatusUpdateComponent, MatDialogModule, TranslateModule.forRoot()],
			providers: [
				{ provide: MawbStatusService, useValue: mockStatusService },
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClientTesting(),
				provideHttpClient(),
				{ provide: takeUntilDestroyed, useValue: () => (source: any) => source },
				{ provide: MAT_DIALOG_DATA, useValue: mockDialogData },
				{ provide: MatDialogRef, useValue: { close: jasmine.createSpy('close') } },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(MawbStatusUpdateComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should set eventTimeType to EventTimeType[0] when checkbox is checked', () => {
		const event = { checked: true } as MatCheckboxChange;

		component.onEventTypeChange(event);

		expect(component.eventTimeType).toBe(EventTimeType[1]);
	});

	it('should set eventTimeType to EventTimeType[1] when checkbox is unchecked', () => {
		const event = { checked: false } as MatCheckboxChange;

		component.onEventTypeChange(event);

		expect(component.eventTimeType).toBe(EventTimeType[0]);
	});
});

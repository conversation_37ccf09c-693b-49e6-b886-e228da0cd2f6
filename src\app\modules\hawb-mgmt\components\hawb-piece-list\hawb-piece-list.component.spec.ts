import { ComponentFixture, TestBed, fakeAsync, tick, discardPeriodicTasks } from '@angular/core/testing';
import { DestroyRef, ChangeDetectorRef } from '@angular/core';
import { of, throwError } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule } from '@ngx-translate/core';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient } from '@angular/common/http';
import { HawbPieceListComponent } from './hawb-piece-list.component';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { PieceList } from 'src/app/modules/sli-mgmt/models/piece/piece-list.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';

describe('HawbPieceListComponent', () => {
	let component: HawbPieceListComponent;
	let fixture: ComponentFixture<HawbPieceListComponent>;
	let mockHawbSearchRequestService: jasmine.SpyObj<HawbSearchRequestService>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;
	let mockDestroyRef: jasmine.SpyObj<DestroyRef>;

	// Mock test data
	const mockPieceListResponse: PaginationResponse<PieceList> = {
		rows: [
			{
				type: 'Piece',
				pieceId: 'PIECE001',
				productDescription: 'GPU Components',
				packagingType: 'Box, plastic',
				grossWeight: 15.5,
				dimensions: {
					length: 120,
					width: 60,
					height: 40,
				},
				pieceQuantity: 2,
				slac: 0,
				latestStatus: 'In Transit',
			},
			{
				type: 'Piece',
				pieceId: 'PIECE002',
				productDescription: 'Computer Accessories',
				packagingType: 'Box, fibreboard',
				grossWeight: 25.0,
				dimensions: {
					length: 200,
					width: 100,
					height: 80,
				},
				pieceQuantity: 1,
				slac: 0,
				latestStatus: 'Delivered',
			},
		],
		total: 2,
	};

	const mockEmptyPieceListResponse: PaginationResponse<PieceList> = {
		rows: [],
		total: 0,
	};

	beforeEach(async () => {
		// Create spies for dependencies
		mockHawbSearchRequestService = jasmine.createSpyObj<HawbSearchRequestService>('HawbSearchRequestService', ['getPieceList']);
		mockChangeDetectorRef = jasmine.createSpyObj<ChangeDetectorRef>('ChangeDetectorRef', ['markForCheck']);
		mockDestroyRef = jasmine.createSpyObj<DestroyRef>('DestroyRef', ['onDestroy']);

		// Configure default mock return values
		mockHawbSearchRequestService.getPieceList.and.returnValue(of(mockPieceListResponse));

		await TestBed.configureTestingModule({
			imports: [HawbPieceListComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			providers: [
				provideHttpClient(),
				provideHttpClientTesting(),
				{ provide: HawbSearchRequestService, useValue: mockHawbSearchRequestService },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
				{ provide: DestroyRef, useValue: mockDestroyRef },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(HawbPieceListComponent);
		component = fixture.componentInstance;
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with default values', () => {
			expect(component.pieceList).toEqual([]);
			expect(component.pageParams).toEqual({
				pageNum: 1,
				pageSize: 10,
			});
			expect(component.totalRecords).toBe(0);
			expect(component.dataLoading).toBe(false);
			expect(component.hawbId).toBe('');
			expect(component.hawbNumber).toBe('');
			expect(component.sliNumber).toBe('');
		});

		it('should call refreshData on ngOnInit', () => {
			spyOn(component, 'refreshData');

			component.ngOnInit();

			expect(component.refreshData).toHaveBeenCalled();
		});
	});

	describe('refreshData Method', () => {
		it('should fetch piece list when hawbId is provided', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			spyOn(component as any, 'getPieceListPage');

			component.refreshData();
			tick(2000); // Wait for setTimeout

			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
			discardPeriodicTasks();
		}));

		it('should not fetch piece list when hawbId is empty', fakeAsync(() => {
			component.hawbId = '';
			spyOn(component as any, 'getPieceListPage');

			component.refreshData();
			tick(2000); // Wait for setTimeout

			expect(component['getPieceListPage']).not.toHaveBeenCalled();
			discardPeriodicTasks();
		}));

		it('should not fetch piece list when hawbId is null', fakeAsync(() => {
			component.hawbId = null as any;
			spyOn(component as any, 'getPieceListPage');

			component.refreshData();
			tick(2000); // Wait for setTimeout

			expect(component['getPieceListPage']).not.toHaveBeenCalled();
			discardPeriodicTasks();
		}));
	});

	describe('onSortChange Method', () => {
		it('should clear sort parameters when direction is empty', () => {
			const sort: Sort = { active: 'productDescription', direction: '' };
			component.pageParams.orderByColumn = 'previousColumn';
			component.pageParams.isAsc = 'asc';

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});

		it('should set sort parameters when direction is asc', () => {
			const sort: Sort = { active: 'productDescription', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should set sort parameters when direction is desc', () => {
			const sort: Sort = { active: 'grossWeight', direction: 'desc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
		});

		it('should handle different column names', () => {
			const sort: Sort = { active: 'pieceQuantity', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('pieceQuantity');
			expect(component.pageParams.isAsc).toBe('asc');
		});
	});

	describe('onPageChange Method', () => {
		it('should update pageParams and call getPieceListPage', () => {
			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 20,
				length: 100,
			};
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(3); // pageIndex + 1
			expect(component.pageParams.pageSize).toBe(20);
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should update sort parameters when provided in pageEvent', () => {
			const pageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 1,
				pageSize: 15,
				length: 50,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			};
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(15);
			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should not update sort parameters when not provided in pageEvent', () => {
			const pageEvent: PageEvent = {
				pageIndex: 0,
				pageSize: 10,
				length: 20,
			};
			component.pageParams.orderByColumn = 'existingColumn';
			component.pageParams.isAsc = 'asc';
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(1);
			expect(component.pageParams.pageSize).toBe(10);
			expect(component.pageParams.orderByColumn).toBe('existingColumn');
			expect(component.pageParams.isAsc).toBe('asc');
		});
	});

	describe('getPieceListPage Method', () => {
		it('should fetch piece list data successfully', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			const pageParams: PaginationRequest = { pageNum: 1, pageSize: 10 };

			component['getPieceListPage'](pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(component.pieceList).toEqual(mockPieceListResponse.rows);
			expect(component.totalRecords).toBe(mockPieceListResponse.total);
			expect(mockHawbSearchRequestService.getPieceList).toHaveBeenCalledWith(pageParams, 'HAWB123');
		}));

		it('should set dataLoading to true and clear pieceList at start, then complete successfully', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.dataLoading = false;
			component.pieceList = [mockPieceListResponse.rows[0]];

			// Call the method and complete immediately
			component['getPieceListPage'](component.pageParams);
			tick();

			// Since the observable completes synchronously, check final state
			expect(component.dataLoading).toBe(false);
			expect(component.pieceList).toEqual(mockPieceListResponse.rows);
			expect(component.totalRecords).toBe(mockPieceListResponse.total);
		}));

		it('should handle empty response', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			mockHawbSearchRequestService.getPieceList.and.returnValue(of(mockEmptyPieceListResponse));

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.pieceList).toEqual([]);
			expect(component.totalRecords).toBe(0);
			expect(component.dataLoading).toBe(false);
		}));

		it('should handle service error', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			component.dataLoading = false;
			mockHawbSearchRequestService.getPieceList.and.returnValue(throwError(() => new Error('Service error')));

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
			expect(mockChangeDetectorRef.markForCheck).not.toHaveBeenCalled();
		}));

		it('should call service with correct parameters', () => {
			component.hawbId = 'HAWB456';
			const customPageParams: PaginationRequest = {
				pageNum: 3,
				pageSize: 25,
				orderByColumn: 'productDescription',
				isAsc: 'desc',
			};

			component['getPieceListPage'](customPageParams);

			expect(mockHawbSearchRequestService.getPieceList).toHaveBeenCalledWith(customPageParams, 'HAWB456');
		});
	});

	describe('Input Properties', () => {
		it('should accept hawbId input', () => {
			const testHawbId = 'TEST-HAWB-001';
			component.hawbId = testHawbId;

			expect(component.hawbId).toBe(testHawbId);
		});

		it('should accept hawbNumber input', () => {
			const testHawbNumber = 'H000123';
			component.hawbNumber = testHawbNumber;

			expect(component.hawbNumber).toBe(testHawbNumber);
		});

		it('should accept sliNumber input', () => {
			const testSliNumber = 'S000456';
			component.sliNumber = testSliNumber;

			expect(component.sliNumber).toBe(testSliNumber);
		});
	});

	describe('Component Integration', () => {
		it('should initialize and fetch data when hawbId is set', fakeAsync(() => {
			component.hawbId = 'HAWB789';
			spyOn(component as any, 'getPieceListPage');

			component.ngOnInit();
			tick(2000);

			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
			discardPeriodicTasks();
		}));

		it('should handle complete workflow: sort, paginate, and fetch', fakeAsync(() => {
			component.hawbId = 'HAWB999';

			// Test sorting
			const sort: Sort = { active: 'productDescription', direction: 'asc' };
			component.onSortChange(sort);

			// Test pagination
			const pageEvent: PageEvent = { pageIndex: 1, pageSize: 20, length: 100 };
			component.onPageChange(pageEvent);
			tick();

			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
			expect(component.pageParams.pageNum).toBe(2);
			expect(component.pageParams.pageSize).toBe(20);
			expect(mockHawbSearchRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, 'HAWB999');
		}));

		it('should maintain state consistency during multiple operations', () => {
			// Initial state
			expect(component.dataLoading).toBe(false);
			expect(component.pieceList).toEqual([]);
			expect(component.totalRecords).toBe(0);

			// After setting inputs
			component.hawbId = 'HAWB-MULTI';
			component.hawbNumber = 'H-MULTI';
			component.sliNumber = 'S-MULTI';

			expect(component.hawbId).toBe('HAWB-MULTI');
			expect(component.hawbNumber).toBe('H-MULTI');
			expect(component.sliNumber).toBe('S-MULTI');
		});
	});

	describe('Edge Cases', () => {
		it('should handle undefined sort direction', () => {
			const sort: Sort = { active: 'productDescription', direction: undefined as any };
			// Set initial values
			component.pageParams.orderByColumn = 'initialColumn';
			component.pageParams.isAsc = 'asc';

			component.onSortChange(sort);

			// Undefined direction is not the same as empty string, so values should be set
			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBeUndefined();
		});

		it('should handle zero pageIndex in pagination', () => {
			const pageEvent: PageEvent = { pageIndex: 0, pageSize: 10, length: 50 };
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(1);
		});

		it('should handle large page sizes', () => {
			const pageEvent: PageEvent = { pageIndex: 0, pageSize: 1000, length: 5000 };
			spyOn(component as any, 'getPieceListPage');

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageSize).toBe(1000);
		});

		it('should handle service returning null response', fakeAsync(() => {
			component.hawbId = 'HAWB123';
			mockHawbSearchRequestService.getPieceList.and.returnValue(of(null as any));

			// The component will throw an error when trying to access res.rows on null
			expect(() => {
				component['getPieceListPage'](component.pageParams);
				tick();
			}).toThrow();

			// Verify loading state was set initially
			expect(component.dataLoading).toBe(true);
		}));
	});
});

.orll-piece-consolidate-dialog {
	.row {
		margin-left: 30px;
		margin-right: 30px;
		display: flex;
		margin-top: 10px;
		justify-content: space-between;

		.input-box {
			width: 100%;

			.autocomplete-arrow {
				width: 100px;
			}

			.unit-row,
			.all-inputs-row {
				border-radius: 5px 0px 0px 5px !important;
				display: flex;
				height: 33px;

				input {
					border: none;
				}

				:focus-visible {
					outline: none !important;
					box-shadow: none !important;
				}

				mat-icon {
					margin-top: 5px;
				}
			}

			.unit-row span,
			.all-inputs-row .divider {
				display: inline-block;
				margin-top: 5px;
				width: 1px;
				height: 31px;
				background-color: var(--mat-expansion-container-background-color);
				margin-right: 10px;
			}

			.all-inputs-row {
				padding-right: 10px;

				.divider {
					margin-left: 10px;
				}

				span {
					display: inline-block;
					height: 33px;
					line-height: 33px;
				}
			}

			.weight-unit {
				margin-right: 10px;
			}
		}

		.iata-shipper-box {
			position: relative;
			margin-bottom: 20px;
			border-radius: 8px;
			padding: 20px;
			flex: 1;
			border: 1px solid var(--iata-grey-200);
		}

		.fill {
			flex: 1 1 auto;
		}
	}

	&__btn {
		margin-top: 30px;
		margin-bottom: 70px;
		gap: 8px;
		margin-left: 10px;
		display: flex;
		justify-content: flex-end;
		padding-right: 20px !important;
	}
}

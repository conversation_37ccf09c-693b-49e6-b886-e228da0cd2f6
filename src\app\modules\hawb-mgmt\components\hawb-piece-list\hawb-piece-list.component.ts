import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { SliPieceTableComponent } from 'src/app/modules/sli-mgmt/components/sli-piece-table/sli-piece-table.component';
import { PieceList } from 'src/app/modules/sli-mgmt/models/piece/piece-list.model';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
	selector: 'orll-hawb-piece-list',
	templateUrl: './hawb-piece-list.component.html',
	styleUrl: './hawb-piece-list.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [SliPieceTableComponent, SpinnerComponent],
})
export class HawbPieceListComponent extends DestroyRefComponent implements OnInit {
	@Input() hawbId = '';
	@Input() hawbNumber = '';
	@Input() sliNumber = '';

	pieceList: PieceList[] = [];
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};
	totalRecords = 0;
	dataLoading = false;
	noMorePieces = false;

	constructor(
		private readonly hawbSearchRequestService: HawbSearchRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit(): void {
		this.refreshData();
	}

	refreshData() {
		if (this.hawbId) {
			this.getPieceListPage(this.pageParams);
		}
	}

	onScroll(event: Event) {
		console.log(event);

		const div = event.target as HTMLDivElement;
		const atBottom = div.scrollHeight - div.scrollTop === div.clientHeight;

		if (atBottom && !this.dataLoading && !this.noMorePieces) {
			this.refreshData();
		}
	}

	onSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
	}

	onPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getPieceListPage(this.pageParams);
	}

	private getPieceListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;

		this.hawbSearchRequestService
			.getPieceList(pageParams, this.hawbId)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					for (let i = 0; i < 10; i++) {
						this.pieceList = [...this.pieceList, ...res.rows];
					}

					this.totalRecords = res.total;
					this.dataLoading = false;

					this.pageParams.pageNum += 1;
					if (this.pieceList.length === res.total) {
						this.noMorePieces = true;
					}
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}
}
